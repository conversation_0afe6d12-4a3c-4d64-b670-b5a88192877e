import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useFreelanceProjects } from '@/hooks/useFreelanceProjects';
import { useToast } from '@/hooks/use-toast';
import { CreateProjectData, ProjectStatus, STATUS_LABELS, WORK_LOCATION_LABELS, FreelanceProject } from '@/types/freelance';
import { Sparkles, X, Plus, Calendar, DollarSign, Users, MapPin, Globe, Wand2, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface ProjectFormProps {
  onClose: () => void;
  initialProject?: FreelanceProject;
  onSubmit?: (data: any) => Promise<void>;
}

export const ProjectForm = ({ onClose, initialProject, onSubmit }: ProjectFormProps) => {
  // Basic project info
  const [projectName, setProjectName] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [contactPerson, setContactPerson] = useState('');
  const [contactEmail, setContactEmail] = useState('');
  const [contactPhone, setContactPhone] = useState('');
  const [projectDescription, setProjectDescription] = useState('');
  const [budgetRange, setBudgetRange] = useState('');
  const [projectStartDate, setProjectStartDate] = useState('');
  const [projectEndDate, setProjectEndDate] = useState('');
  const [applicationDate, setApplicationDate] = useState(new Date().toISOString().split('T')[0]);
  const [status, setStatus] = useState<ProjectStatus>('not_applied');
  const [applicationText, setApplicationText] = useState('');
  const [notes, setNotes] = useState('');
  
  // New fields
  const [source, setSource] = useState('');
  const [listingUrl, setListingUrl] = useState('');
  const [workLocationType, setWorkLocationType] = useState<'remote' | 'onsite' | 'hybrid' | 'flexible' | ''>('');
  const [remotePercentage, setRemotePercentage] = useState('');
  const [workLocationNotes, setWorkLocationNotes] = useState('');
  const [requiredSkills, setRequiredSkills] = useState<string[]>([]);
  const [currentSkill, setCurrentSkill] = useState('');
  
  // AI states
  const [projectText, setProjectText] = useState('');
  const [analyzingProject, setAnalyzingProject] = useState(false);
  const [generatingApplication, setGeneratingApplication] = useState(false);

  const { createProject, isCreating } = useFreelanceProjects();
  const { toast } = useToast();

  // Initialize form with initial project data
  useEffect(() => {
    if (initialProject) {
      setProjectName(initialProject.project_name || '');
      setCompanyName(initialProject.company_name || '');
      setContactPerson(initialProject.contact_person || '');
      setContactEmail(initialProject.contact_email || '');
      setContactPhone(initialProject.contact_phone || '');
      setProjectDescription(initialProject.project_description || '');
      setBudgetRange(initialProject.budget_range || '');
      setProjectStartDate(initialProject.project_start_date || '');
      setProjectEndDate(initialProject.project_end_date || '');
      setApplicationDate(initialProject.application_date || new Date().toISOString().split('T')[0]);
      setStatus(initialProject.status || 'not_applied');
      setApplicationText(initialProject.application_text || '');
      setNotes(initialProject.notes || '');
      setSource(initialProject.source || '');
      setListingUrl(initialProject.listing_url || '');
      setWorkLocationType(initialProject.work_location_type || '');
      setRemotePercentage(initialProject.remote_percentage ? initialProject.remote_percentage.toString() : '');
      setWorkLocationNotes(initialProject.work_location_notes || '');
      setRequiredSkills(initialProject.required_skills || []);
    }
  }, [initialProject]);

  const addSkill = () => {
    if (currentSkill.trim() && !requiredSkills.includes(currentSkill.trim())) {
      setRequiredSkills([...requiredSkills, currentSkill.trim()]);
      setCurrentSkill('');
    }
  };

  const removeSkill = (skillToRemove: string) => {
    setRequiredSkills(requiredSkills.filter(skill => skill !== skillToRemove));
  };

  const analyzeProject = async () => {
    if (!projectText.trim()) {
      toast({
        title: 'Fehler',
        description: 'Bitte füge eine Projektausschreibung ein.',
        variant: 'destructive'
      });
      return;
    }

    setAnalyzingProject(true);

    try {
      const { data, error } = await supabase.functions.invoke('analyze-project', {
        body: { projectText: projectText }
      });

      if (error) {
        throw new Error(error.message || 'Analysefehler');
      }

      if (!data?.analysis) {
        throw new Error('Keine Analysedaten erhalten');
      }

      const analysis = data.analysis;
      
      // Apply analyzed data to form
      if (analysis.project_name) setProjectName(analysis.project_name);
      if (analysis.company_name) setCompanyName(analysis.company_name);
      if (analysis.contact_person) setContactPerson(analysis.contact_person);
      if (analysis.contact_email) setContactEmail(analysis.contact_email);
      if (analysis.contact_phone) setContactPhone(analysis.contact_phone);
      if (analysis.budget_range) setBudgetRange(analysis.budget_range);
      if (analysis.project_start_date) setProjectStartDate(analysis.project_start_date);
      if (analysis.project_end_date) setProjectEndDate(analysis.project_end_date);
      if (analysis.work_location_type) setWorkLocationType(analysis.work_location_type);
      if (analysis.remote_percentage) setRemotePercentage(analysis.remote_percentage);
      if (analysis.required_skills && Array.isArray(analysis.required_skills)) setRequiredSkills(analysis.required_skills);
      if (analysis.project_description_summary) setProjectDescription(analysis.project_description_summary);

      toast({
        title: 'Analyse abgeschlossen',
        description: 'Projektdaten wurden automatisch ausgefüllt.'
      });
    } catch (error: any) {
      toast({
        title: 'Analysefehler',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setAnalyzingProject(false);
    }
  };

  const generateApplication = async () => {
    if (!projectDescription) {
      toast({
        title: 'Fehler',
        description: 'Bitte füge eine Projektbeschreibung hinzu.',
        variant: 'destructive'
      });
      return;
    }

    setGeneratingApplication(true);

    try {
      const { data, error } = await supabase.functions.invoke('generate-application', {
        body: {
          projectDescription,
          companyName,
          projectName,
          requiredSkills
        }
      });

      if (error) {
        console.error('Application generation error:', error);
        throw new Error(error.message || 'Generierungsfehler');
      }

      if (data?.applicationText) {
        setApplicationText(data.applicationText);
        toast({
          title: 'Bewerbung generiert',
          description: 'Eine professionelle Bewerbung wurde erstellt.'
        });
      } else {
        throw new Error('Keine Bewerbung erhalten');
      }
    } catch (error: any) {
      toast({
        title: 'Generierungsfehler',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setGeneratingApplication(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!projectName || !companyName) {
      toast({
        title: 'Fehler',
        description: 'Projektname und Firmenname sind erforderlich.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const projectData: CreateProjectData = {
        project_name: projectName,
        company_name: companyName,
        contact_person: contactPerson || undefined,
        contact_email: contactEmail || undefined,
        contact_phone: contactPhone || undefined,
        project_description: projectDescription || undefined,
        budget_range: budgetRange || undefined,
        project_start_date: projectStartDate || undefined,
        project_end_date: projectEndDate || undefined,
        required_skills: requiredSkills.length > 0 ? requiredSkills : undefined,
        application_date: applicationDate || undefined,
        status: status,
        application_text: applicationText || undefined,
        notes: notes || undefined,
        source: source || undefined,
        listing_url: listingUrl || undefined,
        work_location_type: workLocationType || undefined,
        remote_percentage: remotePercentage ? parseInt(remotePercentage) : undefined,
        work_location_notes: workLocationNotes || undefined
      };

      if (onSubmit) {
        await onSubmit(projectData);
      } else {
        await createProject(projectData);
      }
      onClose();
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  return (
    <div className="space-y-6">
      {/* AI Analysis Section */}
      <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-primary">
            <Sparkles className="h-5 w-5" />
            KI-Projektanalyse
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="projectText">Projektausschreibung einfügen</Label>
            <Textarea
              id="projectText"
              placeholder="Füge hier die komplette Projektausschreibung ein. Die KI wird automatisch alle wichtigen Informationen extrahieren..."
              value={projectText}
              onChange={(e) => setProjectText(e.target.value)}
              className="min-h-[120px] mt-2"
            />
          </div>
          <Button 
            type="button"
            onClick={analyzeProject}
            disabled={analyzingProject || !projectText.trim()}
            className="w-full"
          >
            {analyzingProject ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Analysiere...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Projekt analysieren
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Project Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Projekt-Grunddaten
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="project-name">Projektname *</Label>
                <Input
                  id="project-name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="company-name">Firmenname *</Label>
                <Input
                  id="company-name"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact-person">Ansprechpartner</Label>
                <Input
                  id="contact-person"
                  value={contactPerson}
                  onChange={(e) => setContactPerson(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-email">Kontakt E-Mail</Label>
                <Input
                  id="contact-email"
                  type="email"
                  value={contactEmail}
                  onChange={(e) => setContactEmail(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-phone">Kontakt Telefon</Label>
                <Input
                  id="contact-phone"
                  type="tel"
                  value={contactPhone}
                  onChange={(e) => setContactPhone(e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="source">Quelle</Label>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <Input
                    id="source"
                    value={source}
                    onChange={(e) => setSource(e.target.value)}
                    placeholder="z.B. freelancermap, gulp, hays"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="listing-url">Link zur Ausschreibung</Label>
                <Input
                  id="listing-url"
                  type="url"
                  value={listingUrl}
                  onChange={(e) => setListingUrl(e.target.value)}
                  placeholder="https://..."
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Work Location */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Arbeitsmodell
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="work-location-type">Arbeitsort-Typ</Label>
                <Select value={workLocationType} onValueChange={(value: 'remote' | 'onsite' | 'hybrid' | 'flexible') => setWorkLocationType(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Arbeitsmodell auswählen" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="onsite">Vor Ort</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                    <SelectItem value="flexible">Flexibel</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {(workLocationType === 'hybrid' || workLocationType === 'flexible') && (
                <div className="space-y-2">
                  <Label htmlFor="remote-percentage">Remote-Anteil (%)</Label>
                  <Input
                    id="remote-percentage"
                    type="number"
                    min="0"
                    max="100"
                    value={remotePercentage}
                    onChange={(e) => setRemotePercentage(e.target.value)}
                    placeholder="z.B. 80"
                  />
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="work-location-notes">Arbeitsort-Notizen</Label>
              <Textarea
                id="work-location-notes"
                value={workLocationNotes}
                onChange={(e) => setWorkLocationNotes(e.target.value)}
                placeholder="Zusätzliche Informationen zum Arbeitsmodell..."
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Project Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Projekt-Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="project-start-date">Projektstart</Label>
                <Input
                  id="project-start-date"
                  type="date"
                  value={projectStartDate}
                  onChange={(e) => setProjectStartDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="project-end-date">Projektende</Label>
                <Input
                  id="project-end-date"
                  type="date"
                  value={projectEndDate}
                  onChange={(e) => setProjectEndDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="budget-range">Budget</Label>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <Input
                    id="budget-range"
                    value={budgetRange}
                    onChange={(e) => setBudgetRange(e.target.value)}
                    placeholder="z.B. 50.000 - 80.000€"
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="project-description">Projektbeschreibung</Label>
              <Textarea
                id="project-description"
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label>Benötigte Skills</Label>
              <div className="flex gap-2">
                <Input
                  placeholder="Skill hinzufügen..."
                  value={currentSkill}
                  onChange={(e) => setCurrentSkill(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSkill())}
                />
                <Button type="button" onClick={addSkill} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {requiredSkills.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {requiredSkills.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="pr-1">
                      {skill}
                      <button
                        type="button"
                        onClick={() => removeSkill(skill)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Application */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Bewerbung</CardTitle>
              <Button 
                type="button"
                onClick={generateApplication}
                disabled={generatingApplication || !projectDescription}
                variant="outline"
                size="sm"
              >
                {generatingApplication ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Generiere...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    KI-Bewerbung generieren
                  </>
                )}
              </Button>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="application-date">Bewerbungsdatum</Label>
                <Input
                  id="application-date"
                  type="date"
                  value={applicationDate}
                  onChange={(e) => setApplicationDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select value={status} onValueChange={(value: ProjectStatus) => setStatus(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(STATUS_LABELS).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="application-text">Bewerbungstext</Label>
              <Textarea
                id="application-text"
                value={applicationText}
                onChange={(e) => setApplicationText(e.target.value)}
                placeholder="Deine Bewerbung für dieses Projekt..."
                className="min-h-[200px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notizen</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Zusätzliche Notizen zu diesem Projekt..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Buttons */}
        <div className="flex gap-4 justify-end">
          <Button type="button" variant="outline" onClick={onClose}>
            Abbrechen
          </Button>
          <Button type="submit" disabled={isCreating}>
            {isCreating ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Speichere...
              </>
            ) : (
              'Projekt speichern'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};