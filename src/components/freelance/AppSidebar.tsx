import React from 'react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
  useSidebar
} from '@/components/ui/sidebar';
import { ThemeToggle } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';
import { LogOut, User, BarChart3, Settings as SettingsIcon } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import type { User as SupabaseUser } from '@supabase/supabase-js';

interface AppSidebarProps {
  user: SupabaseUser | null;
  activeTab: string;
  onTabChange: (tab: string) => void;
  onSignOut: () => void;
}

export const AppSidebar: React.FC<AppSidebarProps> = ({
  user,
  activeTab,
  onTabChange,
  onSignOut,
}) => {
  const { setOpenMobile } = useSidebar();
  const isMobile = useIsMobile();

  const handleTabClick = (tab: string) => {
    onTabChange(tab);
    // Close sidebar on mobile after navigation
    if (isMobile) {
      setOpenMobile(false);
    }
  };

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
    },
    {
      id: 'settings',
      label: 'Einstellungen',
      icon: SettingsIcon,
    },
  ];

  return (
    <Sidebar variant="sidebar">
      <SidebarHeader className="p-0">
        <div className="flex items-center space-x-3 px-4 py-4">
          <div className="h-8 w-8 bg-gradient-to-r from-primary to-primary/80 rounded-lg flex items-center justify-center">
            <User className="h-4 w-4 text-primary-foreground" />
          </div>
          <div className="min-w-0 flex-1">
            <h2 className="text-lg font-bold text-sidebar-foreground">Freelancify</h2>
          </div>
        </div>
        
        {/* User Profile Section */}
        {user && (
          <>
            <SidebarSeparator />
            <div className="px-4 py-3">
              <div className="flex items-center space-x-3 p-3 rounded-lg bg-sidebar-accent/50">
                <div className="h-10 w-10 bg-sidebar-accent rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-sidebar-accent-foreground" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-sm font-medium text-sidebar-foreground">Willkommen</p>
                  <p className="text-xs text-sidebar-foreground/70 truncate">{user.email}</p>
                </div>
              </div>
            </div>
          </>
        )}
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => (
                <SidebarMenuItem key={item.id}>
                  <SidebarMenuButton
                    isActive={activeTab === item.id}
                    onClick={() => handleTabClick(item.id)}
                    className="w-full"
                  >
                    <item.icon className="h-4 w-4" />
                    <span>{item.label}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="p-0">
        <SidebarSeparator />

        {/* Theme Toggle */}
        <div className="flex items-center justify-between px-4 py-3">
          <span className="text-sm text-sidebar-foreground">Design</span>
          <ThemeToggle />
        </div>

        <SidebarSeparator />

        {/* Sign Out Button */}
        <div className="px-4 py-3">
          <Button
            variant="outline"
            onClick={onSignOut}
            className="w-full justify-start text-sidebar-foreground border-sidebar-border hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
          >
            <LogOut className="h-4 w-4 mr-3" />
            Abmelden
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};
