import { useState } from 'react';
import { useFreelanceProjects } from '@/hooks/useFreelanceProjects';
import { ProjectCard } from './ProjectCard';
import { ProjectForm } from './ProjectForm';
import { FreelanceProject } from '@/types/freelance';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Plus, Search, Filter, Building2, User, Mail, Phone } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { STATUS_LABELS, STATUS_COLORS, WORK_LOCATION_LABELS, WORK_LOCATION_COLORS } from '@/types/freelance';
import { format } from 'date-fns';
import { de } from 'date-fns/locale';

export const Dashboard = () => {
  const { projects, isLoading, createProject, updateProject, deleteProject } = useFreelanceProjects();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingProject, setEditingProject] = useState<FreelanceProject | null>(null);
  const [viewingProject, setViewingProject] = useState<FreelanceProject | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.project_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.company_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleCreateProject = async (data: any) => {
    try {
      await createProject(data);
      setIsFormOpen(false);
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  const handleUpdateProject = async (data: any) => {
    if (editingProject) {
      try {
        await updateProject({ id: editingProject.id, ...data });
        setEditingProject(null);
      } catch (error) {
        console.error('Error updating project:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Lade Projekte...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 sm:px-6 pb-4 sm:pb-6 max-w-full overflow-x-hidden">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 sm:mb-8 gap-4">
          <div className="min-w-0 flex-1">
            <h1 className="text-2xl sm:text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent">
              Freelancify
            </h1>
            <p className="text-muted-foreground mt-2 text-sm sm:text-base">
              Verwalte deine Projektanfragen und Bewerbungen
            </p>
          </div>
          <Button
            onClick={() => setIsFormOpen(true)}
            className="bg-primary hover:bg-primary/90 w-full sm:w-auto flex-shrink-0"
          >
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden xs:inline">Neues Projekt</span>
            <span className="xs:hidden">Neu</span>
          </Button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-primary">{projects.length}</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Gesamt</div>
          </div>
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-blue-400">
              {projects.filter(p => p.status === 'application_sent').length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">Beworben</div>
          </div>
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-success">
              {projects.filter(p => p.status === 'offer_received').length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">Zusagen</div>
          </div>
          <div className="bg-gradient-card border border-border/50 rounded-lg p-3 sm:p-4">
            <div className="text-xl sm:text-2xl font-bold text-warning">
              {projects.filter(p => p.status === 'interview_scheduled' || p.status === 'interview_completed').length}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">Interviews</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Projekte durchsuchen..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 text-sm"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-full sm:w-[180px] lg:w-[200px]">
              <Filter className="h-4 w-4 mr-2 flex-shrink-0" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Alle Status</SelectItem>
              {Object.entries(STATUS_LABELS).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Results count */}
        <div className="flex items-center gap-2 mb-4">
          <Badge variant="outline">
            {filteredProjects.length} von {projects.length} Projekten
          </Badge>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length === 0 ? (
          <div className="text-center py-8 sm:py-12">
            <div className="text-muted-foreground mb-4 text-sm sm:text-base px-4">
              {projects.length === 0 ? 'Noch keine Projekte vorhanden' : 'Keine Projekte gefunden'}
            </div>
            <Button onClick={() => setIsFormOpen(true)} variant="outline" className="mx-4">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden xs:inline">Erstes Projekt hinzufügen</span>
              <span className="xs:hidden">Projekt hinzufügen</span>
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 auto-rows-fr">
            {filteredProjects.map((project) => (
              <div key={project.id} className="flex">
                <ProjectCard
                  project={project}
                  onEdit={setEditingProject}
                  onDelete={deleteProject}
                  onView={setViewingProject}
                />
              </div>
            ))}
          </div>
        )}

        {/* Create Project Dialog */}
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[95vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Neues Projekt hinzufügen</DialogTitle>
            </DialogHeader>
            <ProjectForm onClose={() => setIsFormOpen(false)} onSubmit={handleCreateProject} />
          </DialogContent>
        </Dialog>

        {/* Edit Project Dialog */}
        <Dialog open={!!editingProject} onOpenChange={() => setEditingProject(null)}>
          <DialogContent className="max-w-[95vw] sm:max-w-4xl max-h-[95vh] overflow-y-auto mx-4">
            <DialogHeader>
              <DialogTitle className="text-lg sm:text-xl">Projekt bearbeiten</DialogTitle>
            </DialogHeader>
            {editingProject && (
              <ProjectForm
                onClose={() => setEditingProject(null)}
                initialProject={editingProject}
                onSubmit={handleUpdateProject}
              />
            )}
          </DialogContent>
        </Dialog>

        {/* View Project Dialog */}
        <Dialog open={!!viewingProject} onOpenChange={() => setViewingProject(null)}>
          <DialogContent className="max-w-[98vw] sm:max-w-4xl max-h-[98vh] sm:max-h-[95vh] overflow-hidden mx-1 sm:mx-4 p-0 rounded-lg sm:rounded-xl">
            <div className="flex flex-col h-full max-h-[95vh]">
              {/* Header Section */}
              <DialogHeader className="px-4 sm:px-6 py-4 sm:py-6 border-b bg-gradient-to-r from-primary/5 to-primary/10 flex-shrink-0">
                <div className="flex items-start justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <DialogTitle className="text-xl sm:text-2xl font-bold text-foreground mb-2 line-clamp-2">
                      {viewingProject?.project_name}
                    </DialogTitle>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                        <span className="text-sm sm:text-base text-muted-foreground font-medium">
                          {viewingProject?.company_name}
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <Badge variant="outline" className={`${STATUS_COLORS[viewingProject?.status || 'draft']} text-xs sm:text-sm`}>
                          {STATUS_LABELS[viewingProject?.status || 'draft']}
                        </Badge>
                        {viewingProject?.work_location_type && (
                          <Badge variant="outline" className={`${WORK_LOCATION_COLORS[viewingProject.work_location_type]} text-xs sm:text-sm`}>
                            {WORK_LOCATION_LABELS[viewingProject.work_location_type]}
                            {viewingProject.remote_percentage && ` (${viewingProject.remote_percentage}%)`}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </DialogHeader>

              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto px-4 sm:px-6 py-4 sm:py-6">
                {viewingProject && (
                  <div className="space-y-6 sm:space-y-8">
                    {/* Project Overview Section */}
                    <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                      <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                        <div className="h-2 w-2 bg-primary rounded-full"></div>
                        Projekt Übersicht
                      </h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                        {viewingProject.budget_range && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="h-1.5 w-1.5 bg-green-500 rounded-full"></div>
                              <span className="text-sm font-medium text-muted-foreground">Budget</span>
                            </div>
                            <p className="text-base sm:text-lg font-semibold text-foreground pl-3.5">
                              {viewingProject.budget_range}
                            </p>
                          </div>
                        )}
                        {viewingProject.project_start_date && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="h-1.5 w-1.5 bg-blue-500 rounded-full"></div>
                              <span className="text-sm font-medium text-muted-foreground">Projektstart</span>
                            </div>
                            <p className="text-base sm:text-lg font-semibold text-foreground pl-3.5">
                              {format(new Date(viewingProject.project_start_date), 'dd.MM.yyyy', { locale: de })}
                            </p>
                          </div>
                        )}
                        {viewingProject.project_end_date && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="h-1.5 w-1.5 bg-orange-500 rounded-full"></div>
                              <span className="text-sm font-medium text-muted-foreground">Projektende</span>
                            </div>
                            <p className="text-base sm:text-lg font-semibold text-foreground pl-3.5">
                              {format(new Date(viewingProject.project_end_date), 'dd.MM.yyyy', { locale: de })}
                            </p>
                          </div>
                        )}
                        {viewingProject.source && (
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="h-1.5 w-1.5 bg-purple-500 rounded-full"></div>
                              <span className="text-sm font-medium text-muted-foreground">Quelle</span>
                            </div>
                            <p className="text-base font-medium text-foreground pl-3.5">
                              {viewingProject.source}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Contact Information Section */}
                    {(viewingProject.contact_person || viewingProject.contact_email || viewingProject.contact_phone) && (
                      <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                          Kontaktinformationen
                        </h3>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                          {viewingProject.contact_person && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium text-muted-foreground">Ansprechpartner</span>
                              </div>
                              <p className="text-base font-medium text-foreground break-words">
                                {viewingProject.contact_person}
                              </p>
                            </div>
                          )}
                          {viewingProject.contact_email && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium text-muted-foreground">E-Mail</span>
                              </div>
                              <a
                                href={`mailto:${viewingProject.contact_email}`}
                                className="text-base font-medium text-primary hover:underline break-all"
                              >
                                {viewingProject.contact_email}
                              </a>
                            </div>
                          )}
                          {viewingProject.contact_phone && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4 text-muted-foreground" />
                                <span className="text-sm font-medium text-muted-foreground">Telefon</span>
                              </div>
                              <a
                                href={`tel:${viewingProject.contact_phone}`}
                                className="text-base font-medium text-primary hover:underline"
                              >
                                {viewingProject.contact_phone}
                              </a>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Project Description Section */}
                    {viewingProject.project_description && (
                      <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                          Projektbeschreibung
                        </h3>
                        <div className="prose prose-sm sm:prose-base max-w-none">
                          <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                            {viewingProject.project_description}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Required Skills Section */}
                    {viewingProject.required_skills && viewingProject.required_skills.length > 0 && (
                      <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                          Benötigte Skills
                        </h3>
                        <div className="flex flex-wrap gap-2 sm:gap-3">
                          {viewingProject.required_skills.map((skill, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="text-sm px-3 py-1.5 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors"
                            >
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Application Text Section */}
                    {viewingProject.application_text && (
                      <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                          Bewerbungstext
                        </h3>
                        <div className="prose prose-sm sm:prose-base max-w-none">
                          <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                            {viewingProject.application_text}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Notes Section */}
                    {viewingProject.notes && (
                      <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                          Notizen
                        </h3>
                        <div className="prose prose-sm sm:prose-base max-w-none">
                          <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                            {viewingProject.notes}
                          </p>
                        </div>
                      </div>
                    )}

                    {/* External Link Section */}
                    {viewingProject.listing_url && (
                      <div className="bg-card/50 rounded-lg p-4 sm:p-6 border">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                          <div className="h-2 w-2 bg-primary rounded-full"></div>
                          Link zur Ausschreibung
                        </h3>
                        <a
                          href={viewingProject.listing_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium text-sm sm:text-base break-all hover:underline transition-colors"
                        >
                          <span>Ausschreibung öffnen</span>
                          <svg className="h-4 w-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                        </a>
                        <p className="text-xs text-muted-foreground mt-2 break-all">
                          {viewingProject.listing_url}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};