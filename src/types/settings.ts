import { FreelanceProject } from './freelance';
import { Tables } from '@/integrations/supabase/types';

export interface CVExperience {
  company: string;
  position: string;
  duration: string;
  description: string;
  technologies?: string[];
}

export interface CVEducation {
  institution: string;
  degree: string;
  duration: string;
  description?: string;
}

// Use the Supabase generated type as base
export type UserSettings = Tables<'user_settings'> & {
  // Add typed versions for JSON fields
  professional_experience_typed?: CVExperience[];
  education_typed?: CVEducation[];
};

export interface CreateUserSettingsData {
  availability_start_date?: string;
  availability_end_date?: string;
  availability_hours_per_week?: number;
  availability_notes?: string;
  full_name?: string;
  address?: string;
  website?: string;
  phone?: string;
  professional_email?: string;
  hourly_rate_eur?: number;
}

export interface UpdateUserSettingsData extends Partial<CreateUserSettingsData> {
  id: string;
}

export interface ExportData {
  projects: FreelanceProject[];
  settings: UserSettings | null;
  exportDate: string;
  version: string;
}