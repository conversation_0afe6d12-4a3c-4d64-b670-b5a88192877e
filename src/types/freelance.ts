export type ProjectStatus =
  | 'not_applied'
  | 'application_sent'
  | 'inquiry_received'
  | 'interview_scheduled'
  | 'interview_completed'
  | 'offer_received'
  | 'rejected'
  | 'project_completed';

export interface FreelanceProject {
  id: string;
  user_id: string;
  project_name: string;
  company_name: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  project_description?: string;
  budget_range?: string;
  project_start_date?: string;
  project_end_date?: string;
  required_skills?: string[];
  application_date?: string;
  status: ProjectStatus;
  application_text?: string;
  notes?: string;
  source?: string;
  listing_url?: string;
  work_location_type?: 'remote' | 'onsite' | 'hybrid' | 'flexible';
  remote_percentage?: number;
  work_location_notes?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateProjectData {
  project_name: string;
  company_name: string;
  contact_person?: string;
  contact_email?: string;
  contact_phone?: string;
  project_description?: string;
  budget_range?: string;
  project_start_date?: string;
  project_end_date?: string;
  required_skills?: string[];
  application_date?: string;
  status?: ProjectStatus;
  application_text?: string;
  notes?: string;
  source?: string;
  listing_url?: string;
  work_location_type?: 'remote' | 'onsite' | 'hybrid' | 'flexible';
  remote_percentage?: number;
  work_location_notes?: string;
}

export interface UpdateProjectData extends Partial<CreateProjectData> {
  id: string;
}

export interface ProjectAnalysis {
  project_name?: string;
  company_name?: string;
  budget_range?: string;
  project_start_date?: string;
  project_end_date?: string;
  required_skills?: string[];
  key_requirements?: string[];
  project_description_summary?: string;
}

export const STATUS_LABELS: Record<ProjectStatus, string> = {
  not_applied: 'Noch nicht beworben',
  application_sent: 'Bewerbung gesendet',
  inquiry_received: 'Rückfrage erhalten',
  interview_scheduled: 'Interview geplant',
  interview_completed: 'Interview durchgeführt',
  offer_received: 'Zusage erhalten',
  rejected: 'Absage erhalten',
  project_completed: 'Projekt abgeschlossen'
};

export const STATUS_COLORS: Record<ProjectStatus, string> = {
  not_applied: 'bg-muted text-muted-foreground',
  application_sent: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
  inquiry_received: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20',
  interview_scheduled: 'bg-purple-500/10 text-purple-400 border-purple-500/20',
  interview_completed: 'bg-indigo-500/10 text-indigo-400 border-indigo-500/20',
  offer_received: 'bg-success/10 text-success border-success/20',
  rejected: 'bg-destructive/10 text-destructive border-destructive/20',
  project_completed: 'bg-primary/10 text-primary border-primary/20'
};

export const WORK_LOCATION_LABELS: Record<'remote' | 'onsite' | 'hybrid' | 'flexible', string> = {
  remote: 'Remote',
  onsite: 'Vor Ort',
  hybrid: 'Hybrid',
  flexible: 'Flexibel'
};

export const WORK_LOCATION_COLORS: Record<'remote' | 'onsite' | 'hybrid' | 'flexible', string> = {
  remote: 'bg-green-500/10 text-green-400 border-green-500/20',
  onsite: 'bg-orange-500/10 text-orange-400 border-orange-500/20',
  hybrid: 'bg-blue-500/10 text-blue-400 border-blue-500/20',
  flexible: 'bg-purple-500/10 text-purple-400 border-purple-500/20'
};