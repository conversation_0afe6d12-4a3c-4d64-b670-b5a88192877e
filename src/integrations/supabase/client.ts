// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://wkmzfqjnlwaogqnkbfgw.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndrbXpmcWpubHdhb2dxbmtiZmd3Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyNDcwNjcsImV4cCI6MjA2NzgyMzA2N30.HGpfYSRbTOWPhXO5MAtcAANPk00sE1fbVSlDxVNEeh0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});