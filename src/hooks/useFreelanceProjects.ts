import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { FreelanceProject, CreateProjectData, UpdateProjectData } from '@/types/freelance';
import { useToast } from '@/hooks/use-toast';

export const useFreelanceProjects = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: projects = [],
    isLoading,
    error
  } = useQuery({
    queryKey: ['freelance-projects'],
    queryFn: async (): Promise<FreelanceProject[]> => {
      const { data, error } = await supabase
        .from('freelance_projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return (data || []) as FreelanceProject[];
    }
  });

  const createProject = useMutation({
    mutationFn: async (projectData: CreateProjectData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('freelance_projects')
        .insert([{ ...projectData, user_id: user.id }])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freelance-projects'] });
      toast({
        title: 'Projekt erstellt',
        description: 'Neues Projekt wurde erfolgreich hinzugefügt.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Projekt konnte nicht erstellt werden.',
        variant: 'destructive'
      });
      console.error('Error creating project:', error);
    }
  });

  const updateProject = useMutation({
    mutationFn: async ({ id, ...updateData }: UpdateProjectData) => {
      const { data, error } = await supabase
        .from('freelance_projects')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freelance-projects'] });
      toast({
        title: 'Projekt aktualisiert',
        description: 'Projekt wurde erfolgreich aktualisiert.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Projekt konnte nicht aktualisiert werden.',
        variant: 'destructive'
      });
      console.error('Error updating project:', error);
    }
  });

  const deleteProject = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('freelance_projects')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freelance-projects'] });
      toast({
        title: 'Projekt gelöscht',
        description: 'Projekt wurde erfolgreich gelöscht.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Projekt konnte nicht gelöscht werden.',
        variant: 'destructive'
      });
      console.error('Error deleting project:', error);
    }
  });

  return {
    projects,
    isLoading,
    error,
    createProject: createProject.mutateAsync,
    updateProject: updateProject.mutateAsync,
    deleteProject: deleteProject.mutateAsync,
    isCreating: createProject.isPending,
    isUpdating: updateProject.isPending,
    isDeleting: deleteProject.isPending
  };
};