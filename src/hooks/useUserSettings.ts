import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { UserSettings, CreateUserSettingsData, UpdateUserSettingsData } from '@/types/settings';
import { useToast } from '@/hooks/use-toast';

export const useUserSettings = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const {
    data: settings,
    isLoading,
    error
  } = useQuery({
    queryKey: ['user-settings'],
    queryFn: async (): Promise<UserSettings | null> => {
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .maybeSingle();

      if (error) throw error;
      return data;
    }
  });

  const createOrUpdateSettings = useMutation({
    mutationFn: async (settingsData: CreateUserSettingsData) => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Try to update first, if no rows affected, insert
      const { data: existingSettings } = await supabase
        .from('user_settings')
        .select('id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (existingSettings) {
        const { data, error } = await supabase
          .from('user_settings')
          .update(settingsData)
          .eq('user_id', user.id)
          .select()
          .single();

        if (error) throw error;
        return data;
      } else {
        const { data, error } = await supabase
          .from('user_settings')
          .insert([{ ...settingsData, user_id: user.id }])
          .select()
          .single();

        if (error) throw error;
        return data;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-settings'] });
      toast({
        title: 'Einstellungen gespeichert',
        description: 'Ihre Einstellungen wurden erfolgreich aktualisiert.'
      });
    },
    onError: (error) => {
      toast({
        title: 'Fehler',
        description: 'Einstellungen konnten nicht gespeichert werden.',
        variant: 'destructive'
      });
      console.error('Error saving settings:', error);
    }
  });

  return {
    settings,
    isLoading,
    error,
    saveSettings: createOrUpdateSettings.mutateAsync,
    isSaving: createOrUpdateSettings.isPending
  };
};