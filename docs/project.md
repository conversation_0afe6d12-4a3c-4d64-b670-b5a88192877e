# Freelance Projekt Management App

## Überblick

Eine umfassende Web-Anwendung zur Verwaltung von Freelance-Projekten mit automatisierter Bewerbungsgenerierung und Projektanalyse.

## Features

### 🎯 Hauptfunktionen
- **Projekt-Dashboard**: Übersicht aller Freelance-Projekte mit Status-Tracking
- **Intelligente Bewerbungsgenerierung**: KI-gestützte Erstellung personalisierter Bewerbungen
- **Projekt-Analyse**: Automatische Analyse von Projektbeschreibungen und Anforderungen
- **Benutzereinstellungen**: Verwaltung persönlicher Daten, Verfügbarkeit und CV-Upload
- **Responsive Design**: Optimiert für Desktop und Mobile

### 🔧 Technische Features
- **Authentifizierung**: Sichere Benutzeranmeldung über Supabase Auth
- **Echtzeit-Updates**: Live-Synchronisation von Projektdaten
- **File Upload**: CV-Upload mit Supabase Storage
- **Dark/Light Mode**: Umschaltbare Themes
- **Toast-Benachrichtigungen**: Benutzerfreundliche Statusmeldungen

## Zielgruppe

Freelancer und Selbstständige, die:
- Ihre Projektbewerbungen effizient verwalten möchten
- Professionelle Bewerbungen automatisiert erstellen wollen
- Ihre Verfügbarkeit und Kontaktdaten zentral verwalten möchten
- Den Überblick über ihre Bewerbungen behalten wollen

## Technologie-Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS, shadcn/ui
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **Deployment**: Lovable Platform
- **KI-Integration**: Gemini API für Textgenerierung

## Status

✅ **Produktiv** - Vollständig funktionsfähige Anwendung mit allen Kernfeatures implementiert.