# Backend Dokumentation (Supabase)

## Übersicht

Das Backend basiert vollständig auf **Supabase** und bietet Authentication, Database, Storage und Edge Functions als Service.

## Supabase Configuration

### Projekt Details
- **Projekt ID**: `wkmzfqjnlwaogqnkbfgw`
- **URL**: `https://wkmzfqjnlwaogqnkbfgw.supabase.co`
- **Region**: Automatisch konfiguriert

### Secrets
- `GEMINI_API_KEY`: Für KI-Textgenerierung
- `SUPABASE_URL`: Projekt-URL
- `SUPABASE_ANON_KEY`: Public API Key
- `SUPABASE_SERVICE_ROLE_KEY`: Admin API Key
- `SUPABASE_DB_URL`: Direkte Datenbankverbindung

## Database Schema

### Tabelle: `freelance_projects`

```sql
CREATE TABLE freelance_projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  project_name TEXT NOT NULL,
  company_name TEXT NOT NULL,
  project_description TEXT,
  required_skills TEXT[],
  budget_range TEXT,
  status TEXT NOT NULL DEFAULT 'not_applied',
  application_text TEXT,
  application_date DATE DEFAULT CURRENT_DATE,
  project_start_date DATE,
  project_end_date DATE,
  contact_person TEXT,
  contact_email TEXT,
  contact_phone TEXT,
  listing_url TEXT,
  source TEXT,
  work_location_type TEXT,
  work_location_notes TEXT,
  remote_percentage INTEGER,
  notes TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

**Status-Werte**: `not_applied`, `applied`, `interview`, `rejected`, `accepted`

### Tabelle: `user_settings`

```sql
CREATE TABLE user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL UNIQUE,
  full_name TEXT,
  professional_email TEXT,
  phone TEXT,
  address TEXT,
  website TEXT,
  hourly_rate_eur INTEGER,
  availability_start_date DATE,
  availability_end_date DATE,
  availability_hours_per_week INTEGER DEFAULT 40,
  availability_notes TEXT,
  cv_pdf_url TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);
```

## Row Level Security (RLS)

### Freelance Projects Policies
```sql
-- Benutzer können nur ihre eigenen Projekte sehen
CREATE POLICY "Users can view their own projects" 
ON freelance_projects FOR SELECT 
USING (auth.uid() = user_id);

-- Benutzer können nur ihre eigenen Projekte erstellen
CREATE POLICY "Users can create their own projects" 
ON freelance_projects FOR INSERT 
WITH CHECK (auth.uid() = user_id);

-- Benutzer können nur ihre eigenen Projekte bearbeiten
CREATE POLICY "Users can update their own projects" 
ON freelance_projects FOR UPDATE 
USING (auth.uid() = user_id);

-- Benutzer können nur ihre eigenen Projekte löschen
CREATE POLICY "Users can delete their own projects" 
ON freelance_projects FOR DELETE 
USING (auth.uid() = user_id);
```

### User Settings Policies
```sql
-- Ähnliche RLS-Policies für user_settings
-- Benutzer können nur ihre eigenen Einstellungen verwalten
```

## Storage

### Bucket: `cv-uploads`
- **Zweck**: CV/Lebenslauf-Dateien
- **Public**: Nein (private)
- **Allowed Types**: PDF-Dateien
- **Max Size**: Konfiguriert für typische CV-Größen

### Storage Policies
```sql
-- Benutzer können ihre eigenen CVs hochladen
CREATE POLICY "Users can upload their own CV" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'cv-uploads' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);
```

## Edge Functions

### 1. `analyze-project`
**Pfad**: `/supabase/functions/analyze-project/index.ts`

**Zweck**: Analysiert Projektbeschreibungen und extrahiert strukturierte Daten

**Input**:
```typescript
{
  projectDescription: string;
  listingUrl?: string;
}
```

**Output**:
```typescript
{
  extractedData: {
    company_name: string;
    project_name: string;
    required_skills: string[];
    budget_range?: string;
    work_location_type?: string;
    contact_person?: string;
    contact_email?: string;
    // ... weitere Felder
  }
}
```

**Technologie**: Gemini AI API für Natural Language Processing

### 2. `generate-application`
**Pfad**: `/supabase/functions/generate-application/index.ts`

**Zweck**: Generiert personalisierte Bewerbungstexte basierend auf Projekt und Benutzerdaten

**Input**:
```typescript
{
  projectId: string;
  userSettings: UserSettings;
  cvContent?: string; // Optional: CV-Inhalt für bessere Personalisierung
}
```

**Output**:
```typescript
{
  applicationText: string;
}
```

**Features**:
- CV-Analyse und skill-matching
- Personalisierte Ansprache
- Conversion-optimierte Struktur
- Freelancer-spezifische Best Practices

## Authentication

### Auth Provider
- **Typ**: Email/Password Authentication
- **Sessions**: Persistent mit localStorage
- **Auto-Refresh**: Aktiviert

### User Management
- Automatische Benutzer-ID Generation
- Session-Management über Supabase Auth
- Keine Custom User Tables (nutzt auth.users)

## Database Triggers

### Update Timestamps
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger für freelance_projects
CREATE TRIGGER update_freelance_projects_updated_at
  BEFORE UPDATE ON freelance_projects
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Trigger für user_settings
CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

## API Patterns

### Frontend-Integration
```typescript
// Supabase Client Import
import { supabase } from "@/integrations/supabase/client";

// Typesafe Database Queries
import type { Database } from "@/integrations/supabase/types";

// Beispiel Query
const { data, error } = await supabase
  .from('freelance_projects')
  .select('*')
  .eq('user_id', userId);
```

### Error Handling
- Consistent Error-Responses
- Client-side Error-Boundaries
- Toast-Notifications für User-Feedback

## Monitoring & Logs

### Available Logs
- **Database Logs**: SQL-Queries und Performance
- **Auth Logs**: Login/Logout Events
- **Edge Function Logs**: Function-Execution und Errors
- **Storage Logs**: File-Upload Events

### Debugging
- Supabase Dashboard für Live-Monitoring
- Edge Function Logs für AI-Integration
- Database Performance Metrics

## Deployment

### Migrations
- Automatische Schema-Migration über Supabase CLI
- Versionierte SQL-Migration-Files
- Rollback-Funktionalität

### Environment
- Production-Ready Configuration
- Automatic SSL/TLS
- CDN für Static Assets
- Global Edge Network