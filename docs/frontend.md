# Frontend Dokumentation

## Technologie-Stack

### Core Technologies
- **React 18**: Moderne React-Version mit Hooks und Concurrent Features
- **TypeScript**: Typsiche<PERSON> Entwicklung
- **Vite**: Schneller Build-Tool und Dev-Server
- **React Router DOM**: Client-side Routing

### UI Framework
- **Tailwind CSS**: Utility-first CSS Framework
- **shadcn/ui**: Moderne, accessible UI-Komponenten
- **Lucide React**: Icon-Library
- **next-themes**: Dark/Light Mode Management

### State Management & Data Fetching
- **TanStack Query**: Server State Management
- **React Hook Form**: Formular-Management
- **Zod**: Schema-Validierung

## Projekt-Struktur

```
src/
├── components/
│   ├── freelance/          # Freelance-spezifische Komponenten
│   │   ├── Dashboard.tsx   # Hauptdashboard
│   │   ├── ProjectCard.tsx # Projektkarten-Komponente
│   │   ├── ProjectForm.tsx # Projekt-Formular
│   │   └── Settings.tsx    # Benutzereinstellungen
│   ├── theme-provider.tsx  # Theme-Kontext
│   ├── theme-toggle.tsx    # Theme-Umschalter
│   └── ui/                 # shadcn/ui Komponenten
├── hooks/                  # Custom React Hooks
│   ├── useFreelanceProjects.ts
│   └── useUserSettings.ts
├── integrations/supabase/  # Supabase Integration
├── lib/                    # Utility-Funktionen
├── pages/                  # Seiten-Komponenten
├── types/                  # TypeScript-Typdefinitionen
└── main.tsx               # App-Einstiegspunkt
```

## Komponenten-Architektur

### Dashboard (`src/components/freelance/Dashboard.tsx`)
- **Zweck**: Hauptansicht mit Projektübersicht und Suchfunktionalität
- **Features**: Projekt-Filterung, Status-Management, Bulk-Aktionen
- **State**: Verwendet `useFreelanceProjects` Hook

### ProjectCard (`src/components/freelance/ProjectCard.tsx`)
- **Zweck**: Einzelne Projektkarte mit Quick-Actions
- **Features**: Status-Badges, Schnellaktionen, Responsive Design
- **Props**: `project`, `onEdit`, `onDelete`, `onGenerateApplication`

### ProjectForm (`src/components/freelance/ProjectForm.tsx`)
- **Zweck**: Formular für Projekt-Erstellung und -Bearbeitung
- **Features**: Validierung, Auto-Save, Skill-Tags
- **Validierung**: Zod-Schema für Typsicherheit

### Settings (`src/components/freelance/Settings.tsx`)
- **Zweck**: Benutzereinstellungen und Profilverwaltung
- **Features**: CV-Upload, Verfügbarkeits-Management, Kontaktdaten
- **State**: Verwendet `useUserSettings` Hook

## Custom Hooks

### `useFreelanceProjects`
```typescript
// Verwaltet alle Projekt-CRUD-Operationen
const { projects, loading, createProject, updateProject, deleteProject } = useFreelanceProjects();
```

### `useUserSettings`
```typescript
// Verwaltet Benutzereinstellungen
const { settings, loading, updateSettings, uploadCV } = useUserSettings();
```

## Design System

### Farben
- Semantische Tokens definiert in `src/index.css`
- HSL-basierte Farbpalette für Dark/Light Mode
- Design-Tokens in `tailwind.config.ts`

### Komponenten-Varianten
- shadcn/ui Komponenten mit Custom-Varianten
- Konsistente Spacing und Typography
- Responsive Breakpoints

## Routing

```typescript
// Hauptrouten
/           # Dashboard (Index)
/auth       # Authentifizierung
/*          # 404 Not Found
```

## Performance Optimierungen

- **Code Splitting**: Automatisch durch Vite
- **Lazy Loading**: React.lazy für Komponenten
- **Memoization**: React.memo für teure Komponenten
- **Query Caching**: TanStack Query für Server-State

## Entwicklungs-Guidelines

### Komponenten-Standards
- Functional Components mit TypeScript
- Props-Interfaces exportieren
- Consistent Naming (PascalCase für Komponenten)
- JSX-Kommentare für komplexe Logik

### State Management
- Server State: TanStack Query
- Client State: useState/useReducer
- Global State: React Context (minimal)

### Styling
- Tailwind-only (keine CSS-Module)
- Semantic Tokens verwenden
- Mobile-first Responsive Design
- Dark Mode Support obligatorisch