-- Extend user_settings table with profile information
ALTER TABLE public.user_settings 
ADD COLUMN IF NOT EXISTS full_name TEXT,
ADD COLUMN IF NOT EXISTS address TEXT,
ADD COLUMN IF NOT EXISTS website TEXT,
ADD COLUMN IF NOT EXISTS phone TEXT,
ADD COLUMN IF NOT EXISTS professional_email TEXT;

-- Extend freelance_projects table with remote/onsite information
ALTER TABLE public.freelance_projects 
ADD COLUMN IF NOT EXISTS work_location_type TEXT CHECK (work_location_type IN ('remote', 'onsite', 'hybrid', 'flexible')),
ADD COLUMN IF NOT EXISTS remote_percentage INTEGER CHECK (remote_percentage >= 0 AND remote_percentage <= 100),
ADD COLUMN IF NOT EXISTS work_location_notes TEXT;

-- Add comment for clarity
COMMENT ON COLUMN public.freelance_projects.work_location_type IS 'Type of work location: remote, onsite, hybrid, flexible';
COMMENT ON COLUMN public.freelance_projects.remote_percentage IS 'Percentage of remote work (0-100%, relevant for hybrid positions)';
COMMENT ON COLUMN public.freelance_projects.work_location_notes IS 'Additional notes about work location requirements';