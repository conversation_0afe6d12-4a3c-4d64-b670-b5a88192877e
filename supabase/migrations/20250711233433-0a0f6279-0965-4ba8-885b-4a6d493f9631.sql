-- Add CV-related fields to user_settings table
ALTER TABLE public.user_settings 
ADD COLUMN cv_pdf_url TEXT,
ADD COLUMN cv_extracted_text TEXT,
ADD COLUMN professional_experience JSONB,
ADD COLUMN skills TEXT[],
ADD COLUMN education JSONB,
ADD COLUMN certifications TEXT[];

-- Create storage bucket for CV uploads
INSERT INTO storage.buckets (id, name, public) VALUES ('cv-uploads', 'cv-uploads', false);

-- Create policies for CV uploads
CREATE POLICY "Users can upload their own CV" 
ON storage.objects 
FOR INSERT 
WITH CHECK (bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own CV" 
ON storage.objects 
FOR SELECT 
USING (bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can update their own CV" 
ON storage.objects 
FOR UPDATE 
USING (bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own CV" 
ON storage.objects 
FOR DELETE 
USING (bucket_id = 'cv-uploads' AND auth.uid()::text = (storage.foldername(name))[1]);