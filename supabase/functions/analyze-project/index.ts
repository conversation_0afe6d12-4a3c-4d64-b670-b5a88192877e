import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const geminiApiKey = Deno.env.get('GEMINI_API_KEY');

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { projectText } = await req.json();

    if (!projectText) {
      return new Response(
        JSON.stringify({ error: 'Projekttext ist erforderlich' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Detect language of the input text
    const languageDetectionPrompt = `
    Analyze the following text and determine if it's written in German or English. 
    Return only "german" or "english" (lowercase, single word):
    
    "${projectText}"
    `;

    const langResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: languageDetectionPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 1,
          maxOutputTokens: 50,
        },
      }),
    });

    const langData = await langResponse.json();
    const detectedLanguage = langData.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase() || 'german';
    const isEnglish = detectedLanguage === 'english';

    const prompt = isEnglish ? `
    You are an expert in freelance project analysis. Analyze the following project posting thoroughly and extract ALL available information:

    Project text: "${projectText}"

    Extract the following information and return it as JSON:
    {
      "project_name": "Full project title or position",
      "company_name": "Name of the company or client", 
      "contact_person": "Name of contact person (look for personal names)",
      "contact_email": "Contact person's email address",
      "contact_phone": "Phone number if available, otherwise null",
      "budget_range": "Hourly rate, daily rate or total budget if mentioned",
      "project_start_date": "Start date in YYYY-MM-DD format",
      "project_end_date": "End date in YYYY-MM-DD format (calculate from start date + project length)",
      "work_location_type": "remote, onsite or hybrid - analyze text for work location hints",
      "remote_percentage": "Percentage for remote work as number (e.g. 100 for 100% remote)",
      "required_skills": ["List of all mentioned technologies and skills"], 
      "key_requirements": ["All tasks and requirements from the text"],
      "project_description_summary": "Structured summary formatted as follows:\\n\\n**Project Overview:**\\n[Brief project description]\\n\\n**Key Responsibilities:**\\n• [Task 1]\\n• [Task 2]\\n• [Task 3]\\n\\n**Required Technologies:**\\n• [Tech 1]\\n• [Tech 2]\\n• [Tech 3]\\n\\n**Project Details:**\\n• Duration: [Duration]\\n• Location: [Work model]\\n• Start Date: [Date]\\n\\n--------------------------\\n\\n**Original Request:**\\n${projectText}"
    }

    IMPORTANT: 
    - Search thoroughly for contact persons and email addresses
    - Recognize work models (Remote/Onsite/Hybrid) from phrases like "100% Remote", "on-site", etc.
    - Calculate end date from start date + project length if possible
    - Extract ALL technologies and skills mentioned
    - Write a well-structured project description with all details
    
    Return only the JSON, without additional text.` : `
    Du bist ein Experte für Freelance-Projektanalyse. Analysiere die folgende Projektausschreibung sehr gründlich und extrahiere ALLE verfügbaren Informationen:

    Projekttext: "${projectText}"

    Extrahiere folgende Informationen und gib sie als JSON zurück:
    {
      "project_name": "Vollständiger Projekttitel oder Position",
      "company_name": "Name der Firma oder des Kunden", 
      "contact_person": "Name der Kontaktperson (suche nach Namen von Personen)",
      "contact_email": "E-Mail-Adresse der Kontaktperson",
      "contact_phone": "Telefonnummer falls verfügbar, sonst null",
      "budget_range": "Stundensatz, Tagessatz oder Gesamtbudget falls erwähnt",
      "project_start_date": "Startdatum im Format YYYY-MM-DD",
      "project_end_date": "Enddatum im Format YYYY-MM-DD (berechne aus Startdatum + Projektlänge)",
      "work_location_type": "remote, onsite oder hybrid - analysiere den Text auf Arbeitsort-Hinweise",
      "remote_percentage": "Prozentsatz für Remote-Arbeit als Zahl (z.B. 100 für 100% Remote)",
      "required_skills": ["Liste aller erwähnten Technologien und Skills"], 
      "key_requirements": ["Alle Aufgaben und Anforderungen aus dem Text"],
      "project_description_summary": "Strukturierte Zusammenfassung im folgenden Format:\\n\\n**Projektübersicht:**\\n[Kurze Projektbeschreibung]\\n\\n**Hauptaufgaben:**\\n• [Aufgabe 1]\\n• [Aufgabe 2]\\n• [Aufgabe 3]\\n\\n**Benötigte Technologien:**\\n• [Tech 1]\\n• [Tech 2]\\n• [Tech 3]\\n\\n**Projektdetails:**\\n• Dauer: [Dauer]\\n• Arbeitsort: [Arbeitsmodell]\\n• Startdatum: [Datum]\\n\\n--------------------------\\n\\n**Original-Anfrage:**\\n${projectText}"
    }

    WICHTIG: 
    - Suche gründlich nach Kontaktpersonen und E-Mail-Adressen
    - Erkenne Arbeitsmodelle (Remote/Onsite/Hybrid) aus Formulierungen wie "100% Remote", "vor Ort", etc.
    - Berechne Enddatum aus Startdatum + Projektlänge wenn möglich
    - Extrahiere ALLE Technologien und Skills die erwähnt werden
    - Schreibe eine gut strukturierte Projektbeschreibung mit allen Details
    
    Gib nur das JSON zurück, ohne zusätzlichen Text.`;

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.2,
          topK: 1,
          topP: 1,
          maxOutputTokens: 2048,
        },
      }),
    });

    if (!response.ok) {
      console.error('Gemini API Error:', response.status, response.statusText);
      throw new Error(`Gemini API Error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Gemini API Response:', JSON.stringify(data, null, 2));

    const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
    
    if (!generatedText) {
      throw new Error('Keine Antwort von Gemini API erhalten');
    }

    // Try to extract JSON from the response
    let analysis;
    try {
      // Remove any markdown formatting
      const cleanText = generatedText.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim();
      analysis = JSON.parse(cleanText);
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError);
      console.error('Generated Text:', generatedText);
      throw new Error('Antwort konnte nicht als JSON geparst werden');
    }

    return new Response(JSON.stringify({ analysis }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in analyze-project function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Ein unbekannter Fehler ist aufgetreten' 
      }), 
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});