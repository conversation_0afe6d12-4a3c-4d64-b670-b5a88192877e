import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const geminiApiKey = Deno.env.get('GEMINI_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseKey);

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { projectDescription, companyName, projectName, requiredSkills } = await req.json();
    
    // Get the Authorization header to extract user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header fehlt' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user from JWT token
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Ungültiger Token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const userId = user.id;

    if (!projectDescription) {
      return new Response(
        JSON.stringify({ error: 'Projektbeschreibung ist erforderlich' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Get user settings for availability, CV PDF, and hourly rate
    const { data: userSettings } = await supabase
      .from('user_settings')
      .select('*')
      .eq('user_id', userId)
      .maybeSingle();

    // Build availability context
    let availabilityContext = '';
    let cvFileData = null;
    
    if (userSettings) {
      // Availability information
      if (userSettings.availability_start_date) {
        availabilityContext += `\nVerfügbar ab: ${userSettings.availability_start_date}`;
      }
      if (userSettings.availability_end_date) {
        availabilityContext += `\nVerfügbar bis: ${userSettings.availability_end_date}`;
      }
      if (userSettings.availability_hours_per_week) {
        availabilityContext += `\nVerfügbare Stunden pro Woche: ${userSettings.availability_hours_per_week}`;
      }
      if (userSettings.availability_notes) {
        availabilityContext += `\nVerfügbarkeits-Notizen: ${userSettings.availability_notes}`;
      }

      // Get CV PDF if available
      if (userSettings.cv_pdf_url) {
        try {
          console.log('Loading CV PDF from:', userSettings.cv_pdf_url);
          
          // Extract file path from URL
          const urlParts = userSettings.cv_pdf_url.split('/');
          const bucketIndex = urlParts.findIndex(part => part === 'cv-uploads');
          if (bucketIndex !== -1) {
            const filePath = urlParts.slice(bucketIndex + 1).join('/');
            
            const { data: fileData, error: downloadError } = await supabase.storage
              .from('cv-uploads')
              .download(filePath);

            if (!downloadError && fileData) {
              // Convert to base64
              const arrayBuffer = await fileData.arrayBuffer();
              const uint8Array = new Uint8Array(arrayBuffer);
              let binaryString = '';
              
              const chunkSize = 8192;
              for (let i = 0; i < uint8Array.length; i += chunkSize) {
                const chunk = uint8Array.slice(i, i + chunkSize);
                binaryString += String.fromCharCode.apply(null, Array.from(chunk));
              }
              
              cvFileData = btoa(binaryString);
              console.log('CV PDF loaded successfully, size:', cvFileData.length);
            } else {
              console.error('Error downloading CV:', downloadError);
            }
          }
        } catch (error) {
          console.error('Error processing CV PDF:', error);
        }
      }
    }

    // Detect language of the project description
    const languageDetectionPrompt = `
    Analyze the following text and determine if it's written in German or English. 
    Return only "german" or "english" (lowercase, single word):
    
    "${projectDescription}"
    `;

    const langResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: languageDetectionPrompt
          }]
        }],
        generationConfig: {
          temperature: 0.1,
          topK: 1,
          topP: 1,
          maxOutputTokens: 50,
        },
      }),
    });

    const langData = await langResponse.json();
    const detectedLanguage = langData.candidates?.[0]?.content?.parts?.[0]?.text?.trim().toLowerCase() || 'german';
    const isEnglish = detectedLanguage === 'english';

    // Build prompt parts
    const promptParts = [
      {
        text: isEnglish ? `
    You are an experienced freelancer writing a professional application in English for the following project:

    Project Name: ${projectName || 'Not specified'}
    Company: ${companyName || 'Not specified'}
    Required Skills: ${requiredSkills?.join(', ') || 'Not specified'}
    Project Description: ${projectDescription}${availabilityContext ? `\n\nAvailability: ${availabilityContext}` : ''}${userSettings?.hourly_rate_eur ? `\nHourly Rate: €${userSettings.hourly_rate_eur} (net)` : ''}

    ${cvFileData ? 'CRITICAL: Analyze the attached CV/Resume carefully. ONLY mention technologies, skills, or experiences that are explicitly stated in the CV. NEVER invent or assume additional skills. If you reference specific projects or experience, they must be directly from the CV.' : ''}

    Write a professional, high-converting freelancer application using proven techniques for maximum response rates:

     - Length: 200-300 words only - concise applications get better responses
     - Use **bold formatting** for 3-4 key technologies/skills that match the project requirements
     - MUST include the exact hourly rate${userSettings?.hourly_rate_eur ? ` (€${userSettings.hourly_rate_eur} net per hour)` : ''}
     - MUST use the exact availability information provided${availabilityContext ? ` - do not say "immediately available" if specific dates are given` : ''}
     - ONLY reference experience that is explicitly mentioned in the CV
     - End with your contact details: ${userSettings?.professional_email ? `Email: ${userSettings.professional_email}` : ''}${userSettings?.phone ? `, Phone: ${userSettings.phone}` : ''}${userSettings?.website ? `, Website: ${userSettings.website}` : ''}

     PROVEN FREELANCER SUCCESS STRUCTURE:
     1. Personal greeting + specific interest in THEIR project (not generic)
     2. Immediately address their main challenge/requirement with confidence
     3. Present 1-2 specific approaches/solutions (show strategic thinking)
     4. Highlight relevant experience with concrete results (bold key tech)
     5. Clear value proposition - what outcome they'll get
     6. Professional availability, rate, and next steps
     7. Strong call-to-action with contact details

     PSYCHOLOGY TACTICS FOR HIGH RESPONSE:
     - Show you understand their specific problem (not just list skills)
     - Demonstrate strategic thinking beyond just technical execution
     - Use confident language ("I will" vs "I can")
     - Include one concrete suggestion or insight about their project
     - Create urgency with your availability window

     CRITICAL: Use ONLY the availability and rate information provided above. Do not modify dates or rates.
     IMPORTANT: Do not mention any technologies, tools, or experiences not explicitly listed in the CV.
     Write only the application, without additional comments or formatting.` : `
    Du bist ein erfahrener Freelancer und schreibst eine professionelle Bewerbung auf Deutsch für folgendes Projekt:

    Projektname: ${projectName || 'Nicht angegeben'}
    Firma: ${companyName || 'Nicht angegeben'}
    Benötigte Skills: ${requiredSkills?.join(', ') || 'Nicht angegeben'}
    Projektbeschreibung: ${projectDescription}${availabilityContext ? `\n\nVerfügbarkeit: ${availabilityContext}` : ''}${userSettings?.hourly_rate_eur ? `\nStundensatz: €${userSettings.hourly_rate_eur} (netto)` : ''}

    ${cvFileData ? 'WICHTIG: Analysiere den beigefügten CV/Lebenslauf sorgfältig. Erwähne NUR Technologien, Skills oder Erfahrungen, die explizit im CV aufgeführt sind. Erfinde NIEMALS zusätzliche Skills. Falls du spezifische Projekte oder Erfahrungen erwähnst, müssen diese direkt aus dem CV stammen.' : ''}

     Schreibe eine professionelle, conversion-optimierte Freelancer-Bewerbung mit bewährten Techniken für maximale Rücklaufquoten:

     - Länge: 200-300 Wörter - prägnante Bewerbungen erhalten bessere Antworten
     - Verwende **Fettschrift** für 3-4 wichtige Technologien/Skills, die zu den Projektanforderungen passen
     - MUSS den exakten Stundensatz enthalten${userSettings?.hourly_rate_eur ? ` (€${userSettings.hourly_rate_eur} netto pro Stunde)` : ''}
     - MUSS die exakten Verfügbarkeitsinformationen verwenden${availabilityContext ? ` - sage nicht "sofort verfügbar" wenn spezifische Daten angegeben sind` : ''}
     - Erwähne NUR Erfahrungen, die explizit im CV aufgeführt sind
     - Am Ende Kontaktdaten hinzufügen: ${userSettings?.professional_email ? `E-Mail: ${userSettings.professional_email}` : ''}${userSettings?.phone ? `, Telefon: ${userSettings.phone}` : ''}${userSettings?.website ? `, Website: ${userSettings.website}` : ''}

     WICHTIGE ANREDEHINWEISE:
     - NIEMALS "Sehr geehrte/r Herr/Frau [Name]" verwenden - der angegebene Name ist der Freelancer-Name, nicht der Empfänger
     - Verwende stattdessen: "Sehr geehrte Damen und Herren" oder "Hallo" als neutrale Anrede
     - FIRMENKONTEXT: Die angegebene "Firma" ist oft ein Recruiter/Vermittler, nicht der Endkunde - formuliere entsprechend vorsichtig

     BEWÄHRTE FREELANCER-ERFOLGSSTRUKTUR:
     1. Neutrale Anrede (KEIN spezifischer Name!) + spezifisches Interesse an IHREM Projekt (nicht generisch)
     2. Sofort die Hauptherausforderung/Anforderung selbstbewusst ansprechen
     3. 1-2 konkrete Lösungsansätze präsentieren (strategisches Denken zeigen)
     4. Relevante Erfahrung mit konkreten Ergebnissen hervorheben (wichtige Technologien fett)
     5. Klares Wertversprechen - welches Ergebnis sie erhalten werden
     6. Professionelle Verfügbarkeit, Stundensatz und nächste Schritte
     7. Starker Handlungsaufruf mit Kontaktdaten

     PSYCHOLOGIE-TAKTIKEN FÜR HOHE RÜCKLAUFQUOTE:
     - Zeige, dass du ihr spezifisches Problem verstehst (nicht nur Skills auflisten)
     - Demonstriere strategisches Denken über reine technische Umsetzung hinaus
     - Verwende selbstbewusste Sprache ("Ich werde" vs "Ich kann")
     - Füge einen konkreten Vorschlag oder Einblick zu ihrem Projekt hinzu
     - Schaffe Dringlichkeit mit deinem Verfügbarkeitsfenster

     KRITISCH: Verwende NUR die oben angegebenen Verfügbarkeits- und Tarifinformationen. Ändere keine Daten oder Tarife.
     WICHTIG: Erwähne keine Technologien, Tools oder Erfahrungen, die nicht explizit im CV aufgeführt sind.
     Schreibe nur die Bewerbung, ohne zusätzliche Kommentare oder Formatierung.`
      }
    ];

    // Add CV PDF if available
    if (cvFileData) {
      promptParts.push({
        inline_data: {
          mime_type: "application/pdf",
          data: cvFileData
        }
      });
    }

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${geminiApiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: promptParts
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        },
      }),
    });

    if (!response.ok) {
      console.error('Gemini API Error:', response.status, response.statusText);
      throw new Error(`Gemini API Error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Gemini API Response:', JSON.stringify(data, null, 2));

    const applicationText = data.candidates?.[0]?.content?.parts?.[0]?.text;
    
    if (!applicationText) {
      throw new Error('Keine Antwort von Gemini API erhalten');
    }

    return new Response(JSON.stringify({ applicationText: applicationText.trim() }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Error in generate-application function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Ein unbekannter Fehler ist aufgetreten' 
      }), 
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});